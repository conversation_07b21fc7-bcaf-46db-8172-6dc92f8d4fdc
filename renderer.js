// 伤害计算模块
class DamageCalculator {
  static calculateMagicDamage(level, magic, magicEffect, magicDefense) {
    const baseDamage = magicEffect * 4 + (level * magic * magicEffect / 32)
    const damage = Math.floor((baseDamage * (255 - magicDefense) / 256) + 1);
    const randomFactor = Math.floor(Math.random() * 32) + 224; // 224-255
    return {
      min: Math.floor((damage * 224 / 256) + 1),
      max: Math.floor((damage * 255 / 256) + 1),
      current: Math.floor((damage * randomFactor / 256) + 1)
    }
  }

  static calculatePhysicalDamage(level, strength, physicalEffect, physicalDefense) {
    const power2 = Math.min(strength * 2, 255)
    const attackValue = physicalEffect + power2 * 2
    const baseDamage = physicalEffect + ((level * level * attackValue) / 256) * 1.5
    const damage = Math.floor((baseDamage * (255 - physicalDefense) / 256) + 1);
    const randomFactor = Math.floor(Math.random() * 32) + 224; // 224-255
    return {
      min: Math.floor((damage * 224 / 256) + 1),
      max: Math.floor((damage * 255 / 256) + 1)
    }
  }
}

// 数据存储模块
class DataStorage {
  static saveData(data) {
    localStorage.setItem('ff6_damage_data', JSON.stringify(data))
  }

  static loadData() {
    const data = localStorage.getItem('ff6_damage_data')
    return data ? JSON.parse(data) : null
  }
}

// UI交互模块
document.addEventListener('DOMContentLoaded', () => {
  // 初始化滑动条和显示值
  const initSlider = (id, defaultValue) => {
    const slider = document.getElementById(id)
    const valueDisplay = document.getElementById(`${id}-value`)
    
    // 尝试加载保存的值
    const savedData = DataStorage.loadData()
    const initialValue = savedData ? savedData[id] : defaultValue
    
    slider.value = initialValue
    valueDisplay.textContent = initialValue
    
    slider.addEventListener('input', () => {
      valueDisplay.textContent = slider.value
      calculateDamage()
      saveCurrentState()
    })
    
    return initialValue
  }

  // 计算并显示伤害
  const calculateDamage = () => {
    const level = parseInt(document.getElementById('level').value)
    const strength = parseInt(document.getElementById('strength').value)
    const magic = parseInt(document.getElementById('magic').value)
    const physicalEffect = parseInt(document.getElementById('physical-effect').value)
    const magicEffect = parseInt(document.getElementById('magic-effect').value)
    const physicalDefense = parseInt(document.getElementById('physical-defense').value)
    const magicDefense = parseInt(document.getElementById('magic-defense').value)

    const physicalDamage = DamageCalculator.calculatePhysicalDamage(
      level, strength, physicalEffect, physicalDefense
    )
    const magicDamage = DamageCalculator.calculateMagicDamage(
      level, magic, magicEffect, magicDefense
    )

    document.getElementById('physical-damage').textContent = Math.max(0, physicalDamage.current)
    document.getElementById('physical-min').textContent = Math.max(0, physicalDamage.min)
    document.getElementById('physical-max').textContent = Math.max(0, physicalDamage.max)
    document.getElementById('magic-damage').textContent = Math.max(0, magicDamage.current)
    document.getElementById('magic-min').textContent = Math.max(0, magicDamage.min)
    document.getElementById('magic-max').textContent = Math.max(0, magicDamage.max)
  }

  // 保存当前状态
  const saveCurrentState = () => {
    const data = {
      level: document.getElementById('level').value,
      strength: document.getElementById('strength').value,
      magic: document.getElementById('magic').value,
      'physical-effect': document.getElementById('physical-effect').value,
      'magic-effect': document.getElementById('magic-effect').value,
      'physical-defense': document.getElementById('physical-defense').value,
      'magic-defense': document.getElementById('magic-defense').value
    }
    DataStorage.saveData(data)
  }

  // 初始化所有滑动条
  initSlider('level', 50)
  initSlider('strength', 50)
  initSlider('magic', 50)
  initSlider('physical-effect', 50)
  initSlider('magic-effect', 50)
  initSlider('physical-defense', 50)
  initSlider('magic-defense', 50)

  // 初始计算
  calculateDamage()
})
