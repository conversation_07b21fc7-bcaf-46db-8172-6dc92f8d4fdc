{"name": "define-data-property", "version": "1.1.4", "description": "Define a data property on an object. Will fall back to assignment in an engine without descriptors.", "main": "index.js", "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "tsc": "tsc -p .", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/define-data-property.git"}, "keywords": ["define", "data", "property", "object", "accessor", "javascript", "ecmascript", "enumerable", "configurable", "writable"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/define-data-property/issues"}, "homepage": "https://github.com/ljharb/define-data-property#readme", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/call-bind": "^1.0.5", "@types/define-properties": "^1.1.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/get-intrinsic": "^1.2.2", "@types/gopd": "^1.0.3", "@types/has-property-descriptors": "^1.0.3", "@types/object-inspect": "^1.8.4", "@types/object.getownpropertydescriptors": "^2.1.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "hasown": "^2.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "object.getownpropertydescriptors": "^2.1.7", "reflect.ownkeys": "^1.1.4", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "types/reflect.ownkeys"]}}