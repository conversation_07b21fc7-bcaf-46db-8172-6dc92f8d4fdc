{"name": "@types/yauzl", "version": "2.10.3", "description": "TypeScript definitions for yauzl", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yauzl", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian", "url": "https://github.com/ffflorian"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yauzl"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "4fb24c28ac8c0fdb7539555e955c273a2a4a433e99938ed73d9e7df3a9e1e2a7", "typeScriptVersion": "4.5"}