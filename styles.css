body {
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
}

.container {
  max-width: 800px;
}

.card {
  border: none;
  border-radius: 10px;
  overflow: hidden;
}

.card-header {
  font-weight: bold;
}

.damage-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #dc3545;
  margin: 10px 0;
  padding: 10px;
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 8px;
}

.form-range::-webkit-slider-thumb {
  background: #0d6efd;
}

.form-range::-moz-range-thumb {
  background: #0d6efd;
}

.form-range::-ms-thumb {
  background: #0d6efd;
}

.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

/* 属性对齐样式 */
.row-align {
  display: flex;
  align-items: center;
}

.attribute-row {
  margin-bottom: 1.5rem;
}

.attribute-label {
  min-width: 100px;
  margin-right: 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .damage-value {
    font-size: 2rem;
  }
  .row-align {
    flex-direction: column;
    align-items: flex-start;
  }
  .attribute-label {
    margin-bottom: 0.5rem;
  }
}
