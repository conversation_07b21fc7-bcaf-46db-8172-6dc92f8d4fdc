# 应用图标说明

## 图标文件要求

为了完整的跨平台支持，您需要以下格式的图标：

### Windows (.ico)
- `icon.ico` - 包含多种尺寸 (16x16, 32x32, 48x48, 256x256)

### macOS (.icns)
- `icon.icns` - 包含多种尺寸的 Apple 图标格式

### Linux (.png)
- `icon.png` - 512x512 像素的 PNG 图标

## 图标转换工具

您可以使用以下在线工具将 SVG 转换为所需格式：

1. **SVG 转 PNG**: https://svgtopng.com/
2. **PNG 转 ICO**: https://icoconvert.com/
3. **PNG 转 ICNS**: https://iconverticons.com/online/

## 转换步骤

1. 使用 SVG 转 PNG 工具将 `icon.svg` 转换为 512x512 的 PNG
2. 将 PNG 重命名为 `icon.png` 并放在 assets 目录
3. 使用 PNG 转 ICO 工具创建 `icon.ico`
4. 使用 PNG 转 ICNS 工具创建 `icon.icns`

## 当前状态

- ✅ `icon.svg` - 源文件已创建
- ❌ `icon.png` - 需要转换
- ❌ `icon.ico` - 需要转换  
- ❌ `icon.icns` - 需要转换

注意：如果没有这些图标文件，打包仍然可以进行，但会使用默认的 Electron 图标。
