{"name": "ff6-damage-calculator", "productName": "最终幻想6伤害计算器", "version": "1.0.0", "description": "最终幻想6伤害计算器 - 精确计算物理和魔法伤害", "main": "main.js", "homepage": ".", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ff6", "final-fantasy", "damage-calculator", "game-tool", "electron"], "author": {"name": "FF6 Calculator Team", "email": "<EMAIL>"}, "license": "MIT", "type": "commonjs", "dependencies": {"electron": "^36.4.0"}, "devDependencies": {"electron-builder": "^26.0.12"}, "build": {"appId": "com.ff6calculator.app", "productName": "最终幻想6伤害计算器", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/electron/**/*"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.games"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Game"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "最终幻想6伤害计算器"}, "dmg": {"title": "最终幻想6伤害计算器", "backgroundColor": "#ffffff"}}}