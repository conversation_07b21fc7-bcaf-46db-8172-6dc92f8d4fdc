<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>最终幻想6伤害计算器</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container mt-5">
    <h1 class="text-center mb-4">最终幻想6伤害计算器</h1>
    
    <div class="card shadow">
      <div class="card-header bg-primary text-white">
        <h3>角色属性</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label for="level" class="form-label">等级: <span id="level-value">50</span></label>
          <input type="range" class="form-range" min="1" max="99" id="level">
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="mb-4">
              <label for="strength" class="form-label">力量: <span id="strength-value">50</span></label>
              <input type="range" class="form-range" min="1" max="255" id="strength">
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-4">
              <label for="physical-effect" class="form-label">物理效果值: <span id="physical-effect-value">50</span></label>
              <input type="range" class="form-range" min="1" max="255" id="physical-effect">
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="mb-4">
              <label for="magic" class="form-label">魔力: <span id="magic-value">50</span></label>
              <input type="range" class="form-range" min="1" max="255" id="magic">
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-4">
              <label for="magic-effect" class="form-label">魔法效果值: <span id="magic-effect-value">50</span></label>
              <input type="range" class="form-range" min="1" max="255" id="magic-effect">
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card shadow mt-4">
      <div class="card-header bg-info text-white">
        <h3>目标属性</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="physical-defense" class="form-label">物理防御: <span id="physical-defense-value">50</span></label>
              <input type="range" class="form-range" min="0" max="255" id="physical-defense">
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="magic-defense" class="form-label">魔法防御: <span id="magic-defense-value">50</span></label>
              <input type="range" class="form-range" min="0" max="255" id="magic-defense">
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card shadow mt-4">
      <div class="card-header bg-success text-white">
        <h3>伤害计算结果</h3>
      </div>
      <div class="card-body text-center">
        <div class="row">
          <div class="col-md-6">
            <h4>物理伤害</h4>
            <div class="damage-range">
              <div class="damage-value-min-max">
                <div class="min-damage">最小: <span id="physical-min">0</span></div>
                <div class="max-damage">最大: <span id="physical-max">0</span></div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <h4>魔法伤害</h4>
            <div class="damage-range">
              <div class="damage-value-min-max">
                <div class="min-damage">最小: <span id="magic-min">0</span></div>
                <div class="max-damage">最大: <span id="magic-max">0</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="renderer.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
