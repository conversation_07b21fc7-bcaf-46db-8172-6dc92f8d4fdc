"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Agent", {
  enumerable: true,
  get: function () {
    return _Agent.default;
  }
});
Object.defineProperty(exports, "HttpProxyAgent", {
  enumerable: true,
  get: function () {
    return _HttpProxyAgent.default;
  }
});
Object.defineProperty(exports, "HttpsProxyAgent", {
  enumerable: true,
  get: function () {
    return _HttpsProxyAgent.default;
  }
});

var _Agent = _interopRequireDefault(require("./Agent"));

var _HttpProxyAgent = _interopRequireDefault(require("./HttpProxyAgent"));

var _HttpsProxyAgent = _interopRequireDefault(require("./HttpsProxyAgent"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
//# sourceMappingURL=index.js.map