* 使用nodejs的Electron.js框架给我开发一款最终幻想6的伤害计算程序：

** 要求：
1. 可以保存上次的使用记录，防止频繁的数值输入。
2. 整体界面优雅美观。
3. 通过滑动轴的方式确定人物的属性和目标的属性，实时生成物理伤害值和魔法伤害值。（包含最大伤害和最小伤害）

** 计算方法。
1. 人物有等级，力量，魔力三个重要的属性属性
2. 攻击类型有物理攻击和魔法攻击，有一个对应的效果值属性
3. 物理攻击和魔法攻击的计算方法不同
4. 目标有物理防御值和魔法防御值两个属性
5. 等级的最大值是99，最小是1

** 具体公式。
对于魔法攻击：伤害=魔法效果值×4+(等级×魔力×魔法效果值/32)；

对于物理攻击：
1：产生一个数据“力2”，力2= 力量×2。但力2最大值为255，即使计算后“力2”>255，“力2”依然等于255；
2：产生一个数据“攻值”，攻值=攻击力+力2×2；
3：伤害值=攻击力+((等级×等级×攻值)/256)×3/2；
4：随机差异。伤害值=(伤害值×[224..255]/256)+1；[a..b]——表示a到b之间（包括a和b）的一个随机整数；
5：伤害值=(伤害值×(255-防御力)/256)+1。（对于魔法攻击则换成魔法防御力）
